import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  OneToOne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import {
  EntityStatusEnum,
  PriceTypeEnum,
  ProductTypeEnum,
} from '@modules/business/enums';
import { ProductAdvancedInfo } from './product-advanced-info.entity';
import {
  ProductPrice,
  ProductImagesType,
  ProductTagsType,
  ShipmentConfigType,
  ProductMetadata,
} from '@modules/business/interfaces';

/**
 * Entity đại diện cho bảng user_products trong cơ sở dữ liệu
 * Bảng quản lý sản phẩm của người dùng
 */
@Entity('user_products')
export class UserProduct {
  /**
   * ID của sản phẩm
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tên sản phẩm
   */
  @Column({
    name: 'name',
    length: 255,
    nullable: false,
    comment: 'Tên sản phẩm',
  })
  name: string;

  /**
   * Vector nhúng từ tên sản phẩm
   */
  @Column({
    name: 'name_embedding',
    type: 'simple-array',
    nullable: true,
    comment: 'Vector nhúng từ tên sản phẩm',
  })
  nameEmbedding: number[] | null;

  /**
   * Giá sản phẩm (dưới dạng JSON)
   */
  @Column({
    name: 'price',
    type: 'jsonb',
    nullable: true,
    comment: 'Giá sản phẩm (dưới dạng JSON)',
  })
  price: ProductPrice | null;

  /**
   * Kiểu giá (ví dụ: cố định, theo giờ...)
   */
  @Column({
    name: 'type_price',
    type: 'enum',
    enum: PriceTypeEnum,
    default: PriceTypeEnum.HAS_PRICE,
    nullable: false,
    comment: 'Kiểu giá (ví dụ: cố định, theo giờ...)',
  })
  typePrice: PriceTypeEnum;

  /**
   * Loại sản phẩm (vật lý, số, sự kiện, dịch vụ)
   */
  @Column({
    name: 'product_type',
    type: 'enum',
    enum: ProductTypeEnum,
    default: ProductTypeEnum.PHYSICAL,
    nullable: false,
    comment: 'Loại sản phẩm (PHYSICAL, DIGITAL, EVENT, SERVICE)',
  })
  productType: ProductTypeEnum;

  /**
   * Các tag mô tả sản phẩm
   */
  @Column({
    name: 'tags',
    type: 'jsonb',
    nullable: true,
    comment: 'Các tag mô tả sản phẩm',
  })
  tags: ProductTagsType;

  /**
   * Mô tả sản phẩm
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: 'Mô tả sản phẩm',
  })
  description: string | null;

  /**
   * Vector nhúng mô tả
   */
  @Column({
    name: 'description_embedding',
    type: 'simple-array',
    nullable: true,
    comment: 'Vector nhúng mô tả',
  })
  descriptionEmbedding: number[] | null;

  /**
   * Hình ảnh sản phẩm (JSON)
   */
  @Column({
    name: 'images',
    type: 'jsonb',
    nullable: true,
    comment: 'URL hình ảnh sản phẩm',
  })
  images: ProductImagesType;

  /**
   * Vector nhúng từ tag
   */
  @Column({
    name: 'tags_embedding',
    type: 'simple-array',
    nullable: true,
    comment: 'Vector nhúng từ tag',
  })
  tagsEmbedding: number[] | null;

  /**
   * ID người tạo sản phẩm
   */
  @Column({
    name: 'created_by',
    type: 'integer',
    nullable: true,
    comment: 'Người tạo sản phẩm',
  })
  createdBy: number;

  /**
   * Thời gian tạo (millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian tạo (millis)',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời gian cập nhật (millis)',
  })
  updatedAt: number;

  /**
   * Cấu hình vận chuyển
   */
  @Column({
    name: 'shipment_config',
    type: 'jsonb',
    nullable: false,
    default: () =>
      '{"widthCm": 25, "heightCm": 5, "lengthCm": 30, "weightGram": 200}::jsonb',
    comment: 'Cấu hình vận chuyển',
  })
  shipmentConfig: ShipmentConfigType; // Chỉ dành cho sản phẩm số

  /**
   * Trạng thái của sản phẩm
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: EntityStatusEnum,
    default: EntityStatusEnum.PENDING,
    nullable: false,
    comment: 'Trạng thái của sản phẩm: PENDING (chờ duyệt), APPROVED (đã duyệt), REJECTED (từ chối), DELETED (đã xóa)',
  })
  status: EntityStatusEnum;

  /**
   * Metadata chứa custom fields và thông tin bổ sung
   */
  @Column({
    name: 'metadata',
    type: 'jsonb',
    nullable: false,
    default: () => "'{}'::jsonb",
    comment: 'Metadata chứa custom fields và thông tin bổ sung',
  })
  metadata: ProductMetadata;

  /**
   * ID của thông tin nâng cao (product_advanced_info)
   */
  @Column({
    name: 'detail_id',
    type: 'bigint',
    nullable: true,
    comment: 'ID của thông tin nâng cao (product_advanced_info)',
  })
  detail_id: number | null;

  /**
   * Relationship với ProductAdvancedInfo (thông tin nâng cao)
   */
  @OneToOne(() => ProductAdvancedInfo, (advancedInfo) => advancedInfo.product)
  @JoinColumn({ name: 'detail_id' })
  advancedInfo?: ProductAdvancedInfo;
}
